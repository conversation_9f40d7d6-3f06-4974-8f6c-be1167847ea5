#!/usr/bin/env python3
"""
Basic inference example for Mini-vLLM

This example demonstrates the core functionality of Mini-vLLM:
- Loading a model
- Generating text completions
- Using different sampling parameters
- Understanding the request/response flow
"""

import logging
from mini_vllm import MiniLLM, SamplingParams

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    print("🚀 Mini-vLLM Basic Inference Example")
    print("=" * 50)
    
    # Example 1: Simple text generation
    print("\n📝 Example 1: Basic Text Generation")
    print("-" * 30)
    
    # Initialize the LLM (using a small model for demo)
    print("Loading model...")
    llm = MiniLLM(
        model="gpt2",  # Small model for quick demo
        max_batch_size=2,
        max_seq_len=100
    )
    
    # Define prompts
    prompts = [
        "The future of artificial intelligence is",
        "Once upon a time in a distant galaxy",
        "The most important thing in life is"
    ]
    
    # Set generation parameters
    sampling_params = SamplingParams(
        temperature=0.8,
        max_tokens=50,
        top_p=0.9,
        stop=[".", "!", "?"]  # Stop at sentence endings
    )
    
    print(f"Generating completions for {len(prompts)} prompts...")
    
    # Generate completions
    outputs = llm.generate(prompts, sampling_params)
    
    # Display results
    for i, output in enumerate(outputs):
        print(f"\nPrompt {i+1}: {output.prompt}")
        print(f"Generated: {output.text}")
        print(f"Finished: {output.finished}")
        print(f"Finish reason: {output.finish_reason}")
    
    # Example 2: Different sampling parameters
    print("\n🎲 Example 2: Different Sampling Parameters")
    print("-" * 30)
    
    prompt = "The key to happiness is"
    
    # Greedy sampling (temperature=0)
    greedy_params = SamplingParams(temperature=0.0, max_tokens=30)
    greedy_output = llm.generate([prompt], greedy_params)[0]
    
    # Creative sampling (high temperature)
    creative_params = SamplingParams(temperature=1.5, max_tokens=30)
    creative_output = llm.generate([prompt], creative_params)[0]
    
    print(f"Prompt: {prompt}")
    print(f"Greedy (temp=0.0): {greedy_output.text}")
    print(f"Creative (temp=1.5): {creative_output.text}")
    
    # Example 3: Batch processing demonstration
    print("\n📦 Example 3: Batch Processing")
    print("-" * 30)
    
    # Create many prompts to see batching in action
    batch_prompts = [f"Number {i} is" for i in range(1, 6)]
    
    batch_params = SamplingParams(temperature=0.7, max_tokens=20)
    
    print(f"Processing {len(batch_prompts)} prompts in batches...")
    batch_outputs = llm.generate(batch_prompts, batch_params)
    
    for i, output in enumerate(batch_outputs):
        print(f"  {batch_prompts[i]} -> {output.text}")
    
    # Example 4: Engine statistics
    print("\n📊 Example 4: Engine Statistics")
    print("-" * 30)
    
    stats = llm.get_stats()
    print("Engine Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n✅ All examples completed successfully!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
