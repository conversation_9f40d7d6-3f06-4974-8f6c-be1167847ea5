# Changelog

All notable changes to Mini-vLLM will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - 2024-01-XX

### Added
- Initial release of Mini-vLLM
- Core inference engine with simplified vLLM architecture
- OpenAI-compatible API server with streaming support
- Command-line interface for server and client
- Poetry-based project structure
- Comprehensive documentation and examples

#### Core Features
- **MiniLLM**: User-facing interface for text generation
- **MiniEngine**: Core coordination and request management
- **MiniScheduler**: FIFO request scheduling with batching
- **MiniModelRunner**: HuggingFace transformers integration
- **AsyncLLMEngine**: Async wrapper for non-blocking API serving

#### API Features
- **Chat Completions**: `/v1/chat/completions` endpoint
- **Text Completions**: `/v1/completions` endpoint
- **Model Management**: `/v1/models` endpoint
- **Health Checks**: `/health` endpoint
- **Streaming Support**: Server-Sent Events for real-time responses
- **OpenAI Compatibility**: Drop-in replacement for OpenAI API

#### CLI Tools
- **mini-vllm-serve**: Start the API server
- **mini-vllm-client**: Interactive client for testing

#### Development Tools
- Poetry for dependency management
- Black for code formatting
- isort for import sorting
- mypy for type checking
- pytest for testing
- pre-commit hooks for code quality

### Technical Details
- Python 3.8+ support
- Single GPU/CPU inference (no distributed execution)
- Basic batching with configurable batch size
- Simple FIFO scheduling (no priorities or preemption)
- HuggingFace transformers backend (no custom kernels)
- Educational focus with clear, documented code

### Examples
- Basic inference examples
- API client demonstrations
- Streaming usage patterns
- Step-by-step internal workflow examples

### Documentation
- Comprehensive README with architecture overview
- API documentation with OpenAI compatibility details
- Development setup and contribution guidelines
- Learning path for understanding vLLM concepts
