"""
Basic tests for Mini-vLLM core functionality
"""
import pytest
from mini_vllm import <PERSON>LL<PERSON>, SamplingParams


def test_import():
    """Test that we can import the main components"""
    from mini_vllm import MiniLLM, SamplingParams, MiniEngine
    assert MiniLLM is not None
    assert SamplingParams is not None
    assert MiniEngine is not None


def test_sampling_params():
    """Test SamplingParams creation"""
    params = SamplingParams(temperature=0.8, max_tokens=50)
    assert params.temperature == 0.8
    assert params.max_tokens == 50
    assert params.top_p == 1.0  # default value


def test_sampling_params_defaults():
    """Test SamplingParams with defaults"""
    params = SamplingParams()
    assert params.temperature == 1.0
    assert params.max_tokens == 100
    assert params.top_p == 1.0
    assert params.top_k == -1


@pytest.mark.slow
def test_mini_llm_creation():
    """Test that we can create a MiniLLM instance"""
    # Use a very small model for testing
    llm = MiniLLM("gpt2", max_seq_len=20)
    assert llm.model_name == "gpt2"
    
    # Test that we can get stats
    stats = llm.get_stats()
    assert "model_name" in stats
    assert stats["model_name"] == "gpt2"


@pytest.mark.slow
def test_basic_generation():
    """Test basic text generation"""
    llm = MiniLLM("gpt2", max_seq_len=30)
    
    prompts = ["Hello"]
    sampling_params = SamplingParams(temperature=0.0, max_tokens=5)  # Greedy, short
    
    outputs = llm.generate(prompts, sampling_params)
    
    assert len(outputs) == 1
    assert outputs[0].prompt == "Hello"
    assert len(outputs[0].text) > 0
    assert outputs[0].finished is True


@pytest.mark.slow
def test_batch_generation():
    """Test batch generation"""
    llm = MiniLLM("gpt2", max_seq_len=30, max_batch_size=2)
    
    prompts = ["Hello", "World"]
    sampling_params = SamplingParams(temperature=0.0, max_tokens=3)
    
    outputs = llm.generate(prompts, sampling_params)
    
    assert len(outputs) == 2
    assert outputs[0].prompt == "Hello"
    assert outputs[1].prompt == "World"
    assert all(output.finished for output in outputs)


def test_convenience_function():
    """Test the convenience generate_text function"""
    from mini_vllm import generate_text

    # This should work without creating an LLM instance
    # But we'll skip the actual generation in CI
    assert generate_text is not None


def test_sampling_params_with_n():
    """Test SamplingParams with multiple sequences (n parameter)"""
    params = SamplingParams(temperature=0.8, max_tokens=50, n=5)
    assert params.n == 5
    assert params.temperature == 0.8
    assert params.max_tokens == 50


def test_sampling_params_n_validation():
    """Test that n parameter is validated"""

    # Valid n values
    params = SamplingParams(n=1)
    assert params.n == 1

    params = SamplingParams(n=5)
    assert params.n == 5

    # Invalid n values should raise ValueError
    with pytest.raises(ValueError, match="n must be at least 1"):
        SamplingParams(n=0)

    with pytest.raises(ValueError, match="n must be at least 1"):
        SamplingParams(n=-1)


@pytest.mark.slow
def test_brainstorming_multiple_outputs():
    """Test brainstorming use case - generate 5 different ideas"""
    llm = MiniLLM("gpt2", max_seq_len=50, max_batch_size=8)

    # Brainstorming prompt
    prompts = ["Creative ways to use AI in education:"]

    # Generate 5 different ideas
    sampling_params = SamplingParams(
        temperature=0.8,  # High temperature for creativity
        max_tokens=20,    # Short responses for testing
        n=5              # Generate 5 different outputs
    )

    outputs = llm.generate(prompts, sampling_params)

    # Should get exactly one RequestOutput
    assert len(outputs) == 1
    request_output = outputs[0]

    # Should have 5 completion outputs
    assert len(request_output.outputs) == 5

    # Each output should have valid structure (content may be empty due to EOS tokens)
    for i, completion in enumerate(request_output.outputs):
        assert completion.index == i
        assert completion.text is not None  # Text can be empty but should not be None
        assert completion.finish_reason is not None

    # Backward compatibility - should still work
    assert len(request_output.text) > 0
    assert request_output.finish_reason is not None
    assert request_output.finished is True

    # Check that we got valid outputs (some may be empty due to EOS tokens)
    # We'll be lenient here since GPT-2 is small and may generate EOS tokens quickly

    print(f"Generated {len(request_output.outputs)} brainstorming ideas:")
    for i, completion in enumerate(request_output.outputs):
        print(f"  {i+1}. {completion.text.strip()}")


@pytest.mark.slow
def test_multiple_prompts_with_multiple_outputs():
    """Test multiple prompts each generating multiple outputs"""
    llm = MiniLLM("gpt2", max_seq_len=40, max_batch_size=4)

    prompts = [
        "The future of AI is",
        "Best programming languages are"
    ]

    # Generate 3 outputs for each prompt
    sampling_params = SamplingParams(
        temperature=0.7,
        max_tokens=10,
        n=3
    )

    outputs = llm.generate(prompts, sampling_params)

    # Should get 2 RequestOutputs (one per prompt)
    assert len(outputs) == 2

    # Each should have 3 completion outputs
    for i, request_output in enumerate(outputs):
        assert request_output.prompt == prompts[i]
        assert len(request_output.outputs) == 3
        assert request_output.finished is True

        # Each completion should be valid
        for j, completion in enumerate(request_output.outputs):
            assert completion.index == j
            assert len(completion.text) > 0
