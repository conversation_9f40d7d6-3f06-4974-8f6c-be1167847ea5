"""
User-facing LLM interface for Mini-vLLM
Provides a simple API similar to vLLM's LLM class
"""
from typing import List, Optional, Union
import logging

from .data_structures import SamplingParams, RequestOutput
from .engine import MiniEngine

logger = logging.getLogger(__name__)


class MiniLLM:
    """
    User-facing interface for Mini-vLLM
    
    This class provides a simple API similar to vLLM's LLM class:
    - Easy initialization with model name
    - Simple generate() method for batch inference
    - Handles all the complexity internally
    """
    
    def __init__(
        self,
        model: str,
        max_batch_size: int = 4,
        max_seq_len: int = 512,
        device: str = "auto"
    ):
        """
        Initialize the Mini-vLLM engine
        
        Args:
            model: HuggingFace model name or path
            max_batch_size: Maximum batch size for inference
            max_seq_len: Maximum sequence length
            device: Device to run on ("auto", "cuda", "cpu")
        """
        self.model_name = model
        
        logger.info(f"Initializing MiniLLM with model: {model}")
        
        # Create the engine
        self.engine = MiniEngine(
            model_name=model,
            max_batch_size=max_batch_size,
            max_seq_len=max_seq_len,
            device=device
        )
        
        logger.info("MiniLLM initialized successfully")
    
    def generate(
        self,
        prompts: Union[str, List[str]],
        sampling_params: Optional[SamplingParams] = None
    ) -> List[RequestOutput]:
        """
        Generate text completions for the given prompts
        
        Args:
            prompts: Single prompt string or list of prompts
            sampling_params: Parameters for text generation
            
        Returns:
            List of RequestOutput objects with generated text
        """
        # Handle single prompt
        if isinstance(prompts, str):
            prompts = [prompts]
        
        if not prompts:
            return []
        
        if sampling_params is None:
            sampling_params = SamplingParams()
        
        logger.info(f"Generating completions for {len(prompts)} prompts")
        
        # Use the engine's generate method
        outputs = self.engine.generate(prompts, sampling_params)
        
        logger.info(f"Generated {len(outputs)} completions")
        return outputs
    
    def chat(
        self,
        messages: List[dict],
        sampling_params: Optional[SamplingParams] = None
    ) -> RequestOutput:
        """
        Simple chat interface (converts messages to prompt)
        
        Args:
            messages: List of message dicts with 'role' and 'content'
            sampling_params: Parameters for text generation
            
        Returns:
            Single RequestOutput with the assistant's response
        """
        # Simple chat template - just concatenate messages
        prompt_parts = []
        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')
            
            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")
        
        prompt_parts.append("Assistant:")
        prompt = "\n".join(prompt_parts)
        
        outputs = self.generate([prompt], sampling_params)
        return outputs[0] if outputs else None
    
    def get_tokenizer(self):
        """Get the underlying tokenizer"""
        return self.engine.tokenizer
    
    def get_stats(self) -> dict:
        """Get engine statistics"""
        return self.engine.get_stats()
    
    def abort_request(self, request_id: str) -> bool:
        """Abort a specific request"""
        return self.engine.abort_request(request_id)


# Convenience function for quick usage
def generate_text(
    model: str,
    prompts: Union[str, List[str]],
    temperature: float = 1.0,
    max_tokens: int = 100,
    top_p: float = 1.0,
    **kwargs
) -> List[str]:
    """
    Convenience function for quick text generation
    
    Args:
        model: HuggingFace model name
        prompts: Prompt(s) to generate from
        temperature: Sampling temperature
        max_tokens: Maximum tokens to generate
        top_p: Top-p (nucleus) sampling parameter
        **kwargs: Additional arguments for MiniLLM
        
    Returns:
        List of generated text strings
    """
    # Create sampling parameters
    sampling_params = SamplingParams(
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=top_p
    )
    
    # Create LLM and generate
    llm = MiniLLM(model, **kwargs)
    outputs = llm.generate(prompts, sampling_params)
    
    # Extract just the text
    return [output.text for output in outputs]


# Example usage patterns
if __name__ == "__main__":
    # Example 1: Basic usage
    llm = MiniLLM("gpt2")
    
    prompts = [
        "The future of AI is",
        "Once upon a time",
        "The capital of France is"
    ]
    
    sampling_params = SamplingParams(
        temperature=0.8,
        max_tokens=50,
        top_p=0.9
    )
    
    outputs = llm.generate(prompts, sampling_params)
    
    for output in outputs:
        print(f"Prompt: {output.prompt}")
        print(f"Generated: {output.text}")
        print("-" * 50)
    
    # Example 2: Chat interface
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of France?"}
    ]
    
    chat_output = llm.chat(messages)
    print(f"Chat response: {chat_output.text}")
    
    # Example 3: Convenience function
    texts = generate_text(
        "gpt2",
        ["Hello world", "The meaning of life is"],
        temperature=0.7,
        max_tokens=30
    )
    
    for text in texts:
        print(f"Generated: {text}")
