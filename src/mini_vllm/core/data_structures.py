"""
Core data structures for Mini-vLLM
Simplified versions of vLLM's data classes
"""
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum
import time
import uuid


class SequenceStatus(Enum):
    """Status of a sequence during generation"""
    WAITING = "waiting"      # In queue, not yet processed
    RUNNING = "running"      # Currently being processed
    FINISHED = "finished"    # Generation complete


@dataclass
class SamplingParams:
    """Parameters for text generation sampling"""
    temperature: float = 1.0
    top_p: float = 1.0
    top_k: int = -1
    max_tokens: int = 100
    stop: Optional[List[str]] = None
    n: int = 1  # Number of sequences to generate per request

    def __post_init__(self):
        if self.stop is None:
            self.stop = []
        if self.n < 1:
            raise ValueError("n must be at least 1")


@dataclass
class Request:
    """A single generation request from the user"""
    request_id: str
    prompt: str
    sampling_params: SamplingParams
    arrival_time: float = field(default_factory=time.time)
    
    @classmethod
    def create(cls, prompt: str, sampling_params: SamplingParams) -> "Request":
        """Create a new request with auto-generated ID"""
        return cls(
            request_id=str(uuid.uuid4()),
            prompt=prompt,
            sampling_params=sampling_params
        )


@dataclass
class Sequence:
    """A sequence being generated (one per request in our simple version)"""
    seq_id: str
    request_id: str
    prompt: str
    prompt_token_ids: List[int]
    output_token_ids: List[int] = field(default_factory=list)
    status: SequenceStatus = SequenceStatus.WAITING
    
    @property
    def is_finished(self) -> bool:
        """Check if sequence generation is complete"""
        return self.status == SequenceStatus.FINISHED
    
    @property
    def get_len(self) -> int:
        """Get total length of sequence (prompt + output)"""
        return len(self.prompt_token_ids) + len(self.output_token_ids)
    
    def add_token(self, token_id: int):
        """Add a new generated token to the sequence"""
        self.output_token_ids.append(token_id)
    
    def get_output_text(self, tokenizer) -> str:
        """Decode output tokens to text"""
        if not self.output_token_ids:
            return ""
        return tokenizer.decode(self.output_token_ids, skip_special_tokens=True)


@dataclass
class SequenceGroup:
    """Group of sequences from the same request (simplified - just one sequence)"""
    request_id: str
    sequences: List[Sequence]
    sampling_params: SamplingParams
    arrival_time: float
    
    @classmethod
    def from_request(cls, request: Request, tokenizer) -> "SequenceGroup":
        """Create a sequence group from a request"""
        # Tokenize the prompt
        prompt_token_ids = tokenizer.encode(request.prompt)

        # Create multiple sequences based on sampling_params.n
        sequences = []
        for i in range(request.sampling_params.n):
            sequence = Sequence(
                seq_id=f"{request.request_id}_{i}",
                request_id=request.request_id,
                prompt=request.prompt,
                prompt_token_ids=prompt_token_ids.copy()  # Each sequence gets its own copy
            )
            sequences.append(sequence)

        return cls(
            request_id=request.request_id,
            sequences=sequences,
            sampling_params=request.sampling_params,
            arrival_time=request.arrival_time
        )
    
    @property
    def is_finished(self) -> bool:
        """Check if all sequences in group are finished"""
        return all(seq.is_finished for seq in self.sequences)
    
    def get_main_sequence(self) -> Sequence:
        """Get the first sequence (for backward compatibility)"""
        return self.sequences[0]

    def get_sequences(self) -> List[Sequence]:
        """Get all sequences in this group"""
        return self.sequences


@dataclass
class ModelInput:
    """Input to the model for a batch of sequences"""
    input_ids: List[List[int]]      # Batch of token sequences
    attention_mask: List[List[int]]  # Attention masks
    position_ids: List[List[int]]    # Position indices
    sequence_groups: List[SequenceGroup]  # Associated sequence groups
    sequence_metadata: List[tuple] = field(default_factory=list)  # (group, sequence) pairs for mapping

    @property
    def batch_size(self) -> int:
        return len(self.input_ids)


@dataclass
class ModelOutput:
    """Output from the model"""
    next_token_ids: List[int]       # Next token for each sequence
    logits: Optional[Any] = None    # Raw logits (if needed)
    
    @property
    def batch_size(self) -> int:
        return len(self.next_token_ids)


@dataclass
class CompletionOutput:
    """Output for a single sequence completion"""
    index: int
    text: str
    finish_reason: Optional[str] = None


@dataclass
class RequestOutput:
    """Final output for a completed request"""
    request_id: str
    prompt: str
    outputs: List[CompletionOutput]  # Multiple outputs for multiple sequences
    finished: bool

    @property
    def text(self) -> str:
        """Get the text of the first output (for backward compatibility)"""
        return self.outputs[0].text if self.outputs else ""

    @property
    def finish_reason(self) -> Optional[str]:
        """Get the finish reason of the first output (for backward compatibility)"""
        return self.outputs[0].finish_reason if self.outputs else None

    def __str__(self) -> str:
        return f"RequestOutput(request_id={self.request_id}, outputs={len(self.outputs)}, finished={self.finished})"


@dataclass
class SchedulerOutput:
    """Output from the scheduler about what to execute"""
    scheduled_groups: List[SequenceGroup]  # Groups to process this step
    num_batched_tokens: int                # Total tokens in batch
    
    @property
    def is_empty(self) -> bool:
        return len(self.scheduled_groups) == 0
