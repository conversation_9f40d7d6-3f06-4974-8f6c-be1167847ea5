"""
Simple model runner for Mini-vLLM
Handles model loading and inference using HuggingFace transformers
"""
import torch
import torch.nn.functional as F
from typing import List, Optional, Union
import logging

from transformers import (
    AutoTokenizer, AutoModelForCausalLM, 
    PreTrainedTokenizer, PreTrainedModel
)

from .data_structures import (
    SequenceGroup, ModelInput, ModelOutput, SequenceStatus, SamplingParams
)

logger = logging.getLogger(__name__)


class MiniModelRunner:
    """
    Simplified model runner using HuggingFace transformers
    
    Key simplifications:
    - Uses HuggingFace models directly (no custom kernels)
    - Simple batching (pad sequences to same length)
    - Basic sampling (temperature + top_p)
    - No KV cache optimization
    - Single GPU/CPU only
    """
    
    def __init__(self, model_name: str, device: str = "auto"):
        self.model_name = model_name
        self.device = self._get_device(device)
        
        logger.info(f"Loading model {model_name} on device {self.device}")
        
        # Load tokenizer and model
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
            device_map=self.device if self.device.type == "cuda" else None
        )
        
        # Set pad token if not exists
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        logger.info(f"Model loaded successfully. Vocab size: {len(self.tokenizer)}")
    
    def _get_device(self, device: str) -> torch.device:
        """Determine the device to use"""
        if device == "auto":
            if torch.cuda.is_available():
                return torch.device("cuda:0")
            else:
                return torch.device("cpu")
        return torch.device(device)
    
    def prepare_inputs(self, sequence_groups: List[SequenceGroup]) -> ModelInput:
        """
        Prepare model inputs from sequence groups

        For simplicity, we'll process both prefill and decode in the same way
        """
        input_ids = []
        attention_mask = []
        position_ids = []
        sequence_metadata = []  # Track (group, sequence) pairs for each input

        for group in sequence_groups:
            # Process all sequences in the group
            for seq in group.get_sequences():
                if seq.status == SequenceStatus.WAITING:
                    # Prefill: use full prompt
                    tokens = seq.prompt_token_ids.copy()
                    seq.status = SequenceStatus.RUNNING
                elif seq.status == SequenceStatus.RUNNING:
                    # Decode: use prompt + generated tokens
                    tokens = seq.prompt_token_ids + seq.output_token_ids
                else:
                    # Skip finished sequences
                    continue

                input_ids.append(tokens)
                attention_mask.append([1] * len(tokens))
                position_ids.append(list(range(len(tokens))))
                sequence_metadata.append((group, seq))

        # Handle empty batch
        if not input_ids:
            return ModelInput(
                input_ids=[],
                attention_mask=[],
                position_ids=[],
                sequence_groups=[],
                sequence_metadata=[]
            )

        # Pad sequences to same length
        max_len = max(len(seq) for seq in input_ids)
        pad_token_id = self.tokenizer.pad_token_id

        for i in range(len(input_ids)):
            padding_length = max_len - len(input_ids[i])
            input_ids[i] = [pad_token_id] * padding_length + input_ids[i]
            attention_mask[i] = [0] * padding_length + attention_mask[i]
            position_ids[i] = [0] * padding_length + position_ids[i]

        return ModelInput(
            input_ids=input_ids,
            attention_mask=attention_mask,
            position_ids=position_ids,
            sequence_groups=sequence_groups,  # Keep original groups
            sequence_metadata=sequence_metadata  # Add metadata for mapping
        )
    
    def execute_model(self, model_input: ModelInput) -> ModelOutput:
        """
        Execute the model forward pass and sample next tokens
        """
        if model_input.batch_size == 0:
            return ModelOutput(next_token_ids=[])
        
        # Convert to tensors
        input_ids = torch.tensor(model_input.input_ids, device=self.device)
        attention_mask = torch.tensor(model_input.attention_mask, device=self.device)
        
        # Forward pass
        with torch.no_grad():
            outputs = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                use_cache=False  # Simplified - no KV cache
            )
        
        # Get logits for the last token of each sequence
        logits = outputs.logits[:, -1, :]  # [batch_size, vocab_size]
        
        # Sample next tokens
        next_token_ids = []
        for i, group in enumerate(model_input.sequence_groups):
            token_logits = logits[i]
            sampling_params = group.sampling_params
            
            next_token_id = self._sample_token(token_logits, sampling_params)
            next_token_ids.append(next_token_id)
        
        return ModelOutput(
            next_token_ids=next_token_ids,
            logits=logits
        )
    
    def _sample_token(self, logits: torch.Tensor, sampling_params: SamplingParams) -> int:
        """
        Sample next token from logits using sampling parameters
        """
        # Apply temperature
        if sampling_params.temperature > 0:
            logits = logits / sampling_params.temperature
        
        # Apply top_k filtering
        if sampling_params.top_k > 0:
            top_k = min(sampling_params.top_k, logits.size(-1))
            indices_to_remove = logits < torch.topk(logits, top_k)[0][..., -1, None]
            logits[indices_to_remove] = float('-inf')
        
        # Apply top_p (nucleus) filtering
        if sampling_params.top_p < 1.0:
            sorted_logits, sorted_indices = torch.sort(logits, descending=True)
            cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
            
            # Remove tokens with cumulative probability above the threshold
            sorted_indices_to_remove = cumulative_probs > sampling_params.top_p
            # Keep at least one token
            sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
            sorted_indices_to_remove[..., 0] = 0
            
            indices_to_remove = sorted_indices_to_remove.scatter(0, sorted_indices, sorted_indices_to_remove)
            logits[indices_to_remove] = float('-inf')
        
        # Sample from the filtered distribution
        if sampling_params.temperature > 0:
            probs = F.softmax(logits, dim=-1)
            next_token_id = torch.multinomial(probs, num_samples=1).item()
        else:
            # Greedy sampling
            next_token_id = torch.argmax(logits, dim=-1).item()
        
        return next_token_id
    
    def check_stop_condition(self, sequence_group: SequenceGroup, new_token_id: int) -> bool:
        """
        Check if generation should stop for this sequence
        """
        seq = sequence_group.get_main_sequence()
        sampling_params = sequence_group.sampling_params
        
        # Check if we hit EOS token
        if new_token_id == self.tokenizer.eos_token_id:
            return True
        
        # Check if we hit max tokens
        if len(seq.output_token_ids) >= sampling_params.max_tokens:
            return True
        
        # Check stop strings (simplified - just check if any stop string is in output)
        if sampling_params.stop:
            output_text = seq.get_output_text(self.tokenizer)
            for stop_str in sampling_params.stop:
                if stop_str in output_text:
                    return True
        
        return False
    
    def get_tokenizer(self) -> PreTrainedTokenizer:
        """Get the tokenizer"""
        return self.tokenizer
