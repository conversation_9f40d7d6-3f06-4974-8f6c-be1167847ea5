"""
FastAPI server for Mini-vLLM with OpenAI-compatible endpoints
"""
import asyncio
import json
import time
import uuid
from typing import As<PERSON><PERSON>enerator, Dict, Any, Optional
import logging

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from .async_engine import AsyncLLMEngine
from ..core.data_structures import SamplingParams
from .protocol import (
    ChatCompletionRequest, ChatCompletionResponse, ChatCompletionStreamResponse,
    CompletionRequest, CompletionResponse, CompletionStreamResponse,
    ModelListResponse, ModelInfo, HealthResponse, ErrorResponse,
    create_chat_completion_response, create_chat_completion_stream_chunk,
    create_error_response, messages_to_prompt, calculate_usage
)

logger = logging.getLogger(__name__)


class MiniVLLMServer:
    """Mini-vLLM API Server with OpenAI-compatible endpoints"""
    
    def __init__(self, engine_args: Dict[str, Any]):
        self.app = FastAPI(
            title="Mini-vLLM API Server",
            description="OpenAI-compatible API server for Mini-vLLM",
            version="0.1.0"
        )
        
        # Initialize async engine
        self.engine = AsyncLLMEngine(engine_args)
        self.model_name = engine_args.get("model_name", "mini-vllm")
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Setup routes
        self._setup_routes()
        
        logger.info("MiniVLLMServer initialized")
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            health_info = await self.engine.health_check()
            return HealthResponse(
                status=health_info["status"],
                model=self.model_name
            )
        
        @self.app.get("/v1/models")
        async def list_models():
            """List available models"""
            return ModelListResponse(
                data=[
                    ModelInfo(
                        id=self.model_name,
                        owned_by="mini-vllm"
                    )
                ]
            )
        
        @self.app.post("/v1/chat/completions")
        async def create_chat_completion(request: ChatCompletionRequest):
            """Create chat completion (OpenAI-compatible)"""
            try:
                # Convert request to internal format
                prompt = messages_to_prompt(request.messages)
                sampling_params = SamplingParams(
                    temperature=request.temperature or 1.0,
                    top_p=request.top_p or 1.0,
                    top_k=request.top_k or -1,
                    max_tokens=request.max_tokens or 100,
                    stop=request.stop if isinstance(request.stop, list) else [request.stop] if request.stop else None
                )
                
                request_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
                
                if request.stream:
                    # Streaming response
                    return StreamingResponse(
                        self._chat_completion_stream_generator(
                            request_id, prompt, sampling_params, request.model
                        ),
                        media_type="text/event-stream",
                        headers={
                            "Cache-Control": "no-cache",
                            "Connection": "keep-alive",
                        }
                    )
                else:
                    # Non-streaming response
                    output = await self.engine.generate(prompt, sampling_params, request_id)
                    
                    # Calculate usage (simplified)
                    prompt_tokens = len(prompt.split())  # Rough estimate
                    completion_tokens = len(output.text.split())
                    usage = calculate_usage(prompt_tokens, completion_tokens)
                    
                    return create_chat_completion_response(
                        request_id=request_id,
                        model=request.model,
                        message_content=output.text,
                        finish_reason=output.finish_reason or "stop",
                        usage=usage
                    )
                    
            except Exception as e:
                logger.error(f"Error in chat completion: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/v1/completions")
        async def create_completion(request: CompletionRequest):
            """Create text completion (OpenAI-compatible)"""
            try:
                # Handle single prompt or multiple prompts
                prompts = request.prompt if isinstance(request.prompt, list) else [request.prompt]
                
                sampling_params = SamplingParams(
                    temperature=request.temperature or 1.0,
                    top_p=request.top_p or 1.0,
                    top_k=request.top_k or -1,
                    max_tokens=request.max_tokens or 100,
                    stop=request.stop if isinstance(request.stop, list) else [request.stop] if request.stop else None
                )
                
                request_id = f"cmpl-{uuid.uuid4().hex[:8]}"
                
                if request.stream:
                    # Streaming response for first prompt only (simplified)
                    return StreamingResponse(
                        self._completion_stream_generator(
                            request_id, prompts[0], sampling_params, request.model
                        ),
                        media_type="text/event-stream"
                    )
                else:
                    # Non-streaming response
                    # For simplicity, handle only first prompt
                    output = await self.engine.generate(prompts[0], sampling_params, request_id)
                    
                    # Calculate usage
                    prompt_tokens = len(prompts[0].split())
                    completion_tokens = len(output.text.split())
                    usage = calculate_usage(prompt_tokens, completion_tokens)
                    
                    return CompletionResponse(
                        id=request_id,
                        model=request.model,
                        choices=[{
                            "index": 0,
                            "text": output.text,
                            "finish_reason": output.finish_reason or "stop"
                        }],
                        usage=usage
                    )
                    
            except Exception as e:
                logger.error(f"Error in completion: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _chat_completion_stream_generator(
        self,
        request_id: str,
        prompt: str,
        sampling_params: SamplingParams,
        model: str
    ) -> AsyncGenerator[str, None]:
        """Generate streaming chat completion response"""
        try:
            # Send initial chunk with role
            initial_chunk = create_chat_completion_stream_chunk(
                request_id=request_id,
                model=model,
                role="assistant"
            )
            yield f"data: {initial_chunk.model_dump_json()}\n\n"

            # Stream tokens as they are generated
            async for token in self.engine.generate_stream(prompt, sampling_params, request_id):
                chunk = create_chat_completion_stream_chunk(
                    request_id=request_id,
                    model=model,
                    delta_content=token
                )
                yield f"data: {chunk.model_dump_json()}\n\n"

            # Send final chunk with finish_reason
            final_chunk = create_chat_completion_stream_chunk(
                request_id=request_id,
                model=model,
                finish_reason="stop"
            )
            yield f"data: {final_chunk.model_dump_json()}\n\n"

            # Send [DONE] marker
            yield "data: [DONE]\n\n"

        except Exception as e:
            logger.error(f"Error in streaming: {e}")
            error_response = create_error_response(str(e))
            yield f"data: {error_response.model_dump_json()}\n\n"
            yield "data: [DONE]\n\n"
    
    async def _completion_stream_generator(
        self,
        request_id: str,
        prompt: str,
        sampling_params: SamplingParams,
        model: str
    ) -> AsyncGenerator[str, None]:
        """Generate streaming completion response"""
        try:
            output = await self.engine.generate(prompt, sampling_params, request_id)
            
            # Split and stream tokens
            tokens = output.text.split()
            
            for i, token in enumerate(tokens):
                chunk = CompletionStreamResponse(
                    id=request_id,
                    model=model,
                    choices=[{
                        "index": 0,
                        "text": (" " if i > 0 else "") + token,
                        "finish_reason": None
                    }]
                )
                yield f"data: {chunk.model_dump_json()}\n\n"
                await asyncio.sleep(0.05)
            
            # Final chunk
            final_chunk = CompletionStreamResponse(
                id=request_id,
                model=model,
                choices=[{
                    "index": 0,
                    "text": "",
                    "finish_reason": "stop"
                }]
            )
            yield f"data: {final_chunk.model_dump_json()}\n\n"
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            logger.error(f"Error in completion streaming: {e}")
            error_response = create_error_response(str(e))
            yield f"data: {error_response.model_dump_json()}\n\n"
            yield "data: [DONE]\n\n"
    
    async def start_engine(self):
        """Start the background engine"""
        await self.engine.start_background_loop()
    
    async def shutdown(self):
        """Shutdown the server and engine"""
        await self.engine.shutdown()


def create_app(engine_args: Dict[str, Any]) -> FastAPI:
    """Create FastAPI app with Mini-vLLM server"""
    server = MiniVLLMServer(engine_args)
    
    @server.app.on_event("startup")
    async def startup_event():
        await server.start_engine()
    
    @server.app.on_event("shutdown")
    async def shutdown_event():
        await server.shutdown()
    
    return server.app
