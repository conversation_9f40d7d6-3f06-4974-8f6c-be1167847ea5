#!/bin/bash
# Setup script for Mini-vLLM development environment

set -e

echo "🚀 Setting up Mini-vLLM development environment"
echo "================================================"

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry first:"
    echo "   curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

echo "✅ Poetry found"

# Install dependencies
echo "📦 Installing dependencies..."
poetry install

# Install pre-commit hooks
echo "🔧 Setting up pre-commit hooks..."
poetry run pre-commit install

# Run basic tests
echo "🧪 Running basic tests..."
poetry run pytest tests/test_basic.py::test_import -v

echo ""
echo "✅ Setup complete!"
echo ""
echo "🎯 Quick start:"
echo "  # Run basic inference example"
echo "  poetry run python examples/basic_inference.py"
echo ""
echo "  # Start API server"
echo "  poetry run mini-vllm-serve --model gpt2"
echo ""
echo "  # Test with client"
echo "  poetry run mini-vllm-client health"
echo ""
echo "  # Run tests"
echo "  poetry run pytest"
echo ""
echo "  # Format code"
echo "  poetry run black src/ tests/"
echo "  poetry run isort src/ tests/"
echo ""
echo "Happy coding! 🎉"
