[tool.poetry]
name = "mini-vllm"
version = "0.1.0"
description = "A simplified, educational implementation of vLLM's core architecture with OpenAI-compatible API"
authors = ["Mini-vLLM Contributors"]
readme = "README.md"
homepage = "https://github.com/azuo-coder/mini-vllm.git"
repository = "https://github.com/azuo-coder/mini-vllm.git"
documentation = "hhttps://github.com/azuo-coder/mini-vllm.git"
keywords = ["llm", "inference", "vllm", "openai", "api", "educational"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
packages = [{include = "mini_vllm", from = "src"}]

[tool.poetry.dependencies]
python = "^3.8.1"
torch = ">=1.9.0"
transformers = "^4.20.0"
tokenizers = ">=0.13.0"
numpy = "^1.21.0"
fastapi = "^0.100.0"
uvicorn = {extras = ["standard"], version = "^0.20.0"}
pydantic = "^2.0.0"
httpx = "^0.24.0"
aiohttp = "^3.8.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.0.0"
pre-commit = "^3.0.0"
pytest-cov = "^4.0.0"

[tool.poetry.group.examples.dependencies]
jupyter = "^1.0.0"
matplotlib = "^3.5.0"
openai = "^1.0.0"

[tool.poetry.scripts]
mini-vllm-serve = "mini_vllm.cli.serve:main"
mini-vllm-client = "mini_vllm.cli.client:main_sync"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["mini_vllm"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "torch.*",
    "transformers.*",
    "tokenizers.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=mini_vllm",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src/mini_vllm"]
omit = [
    "*/tests/*",
    "*/examples/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
