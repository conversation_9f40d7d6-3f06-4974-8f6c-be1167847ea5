<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">37%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 21:53 -0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0b8761712507d4ef___init___py.html">src/mini_vllm/__init__.py</a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab___init___py.html">src/mini_vllm/api/__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html">src/mini_vllm/api/async_engine.py</a></td>
                <td>172</td>
                <td>172</td>
                <td>0</td>
                <td class="right" data-ratio="0 172">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html">src/mini_vllm/api/protocol.py</a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html">src/mini_vllm/api/server.py</a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279___init___py.html">src/mini_vllm/cli/__init__.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html">src/mini_vllm/cli/client.py</a></td>
                <td>117</td>
                <td>117</td>
                <td>2</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_serve_py.html">src/mini_vllm/cli/serve.py</a></td>
                <td>53</td>
                <td>53</td>
                <td>2</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333___init___py.html">src/mini_vllm/core/__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html">src/mini_vllm/core/data_structures.py</a></td>
                <td>115</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="110 115">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html">src/mini_vllm/core/engine.py</a></td>
                <td>95</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="84 95">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html">src/mini_vllm/core/llm.py</a></td>
                <td>48</td>
                <td>24</td>
                <td>15</td>
                <td class="right" data-ratio="24 48">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html">src/mini_vllm/core/model_runner.py</a></td>
                <td>101</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="79 101">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html">src/mini_vllm/core/scheduler.py</a></td>
                <td>81</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="67 81">83%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1024</td>
                <td>645</td>
                <td>19</td>
                <td class="right" data-ratio="379 1024">37%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 21:53 -0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_fda62db419aca333_scheduler_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_0b8761712507d4ef___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
