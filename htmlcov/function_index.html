<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">37%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 21:53 -0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0b8761712507d4ef___init___py.html#t73">src/mini_vllm/__init__.py</a></td>
                <td class="name left"><a href="z_0b8761712507d4ef___init___py.html#t73"><data value='generate_text'>generate_text</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0b8761712507d4ef___init___py.html">src/mini_vllm/__init__.py</a></td>
                <td class="name left"><a href="z_0b8761712507d4ef___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab___init___py.html">src/mini_vllm/api/__init__.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t21">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t21"><data value='init__'>AsyncRequestTracker.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t26">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t26"><data value='add_request'>AsyncRequestTracker.add_request</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t32">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t32"><data value='add_streaming_request'>AsyncRequestTracker.add_streaming_request</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t38">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t38"><data value='complete_request'>AsyncRequestTracker.complete_request</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t48">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t48"><data value='stream_partial'>AsyncRequestTracker.stream_partial</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t54">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t54"><data value='finish_streaming'>AsyncRequestTracker.finish_streaming</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t61">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t61"><data value='cancel_request'>AsyncRequestTracker.cancel_request</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t80">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t80"><data value='init__'>AsyncLLMEngine.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t91">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t91"><data value='start_background_loop'>AsyncLLMEngine.start_background_loop</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t99">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t99"><data value='init_and_run'>AsyncLLMEngine.start_background_loop.init_and_run</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t120">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t120"><data value='background_loop'>AsyncLLMEngine._background_loop</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t146">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t146"><data value='process_new_requests'>AsyncLLMEngine._process_new_requests</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t164">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t164"><data value='handle_completed_request'>AsyncLLMEngine._handle_completed_request</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t178">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t178"><data value='handle_partial_request'>AsyncLLMEngine._handle_partial_request</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t195">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t195"><data value='generate'>AsyncLLMEngine.generate</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t229">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t229"><data value='generate_stream'>AsyncLLMEngine.generate_stream</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t287">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t287"><data value='get_model_config'>AsyncLLMEngine.get_model_config</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t301">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t301"><data value='health_check'>AsyncLLMEngine.health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t309">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t309"><data value='shutdown'>AsyncLLMEngine.shutdown</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t144">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t144"><data value='create_chat_completion_response'>create_chat_completion_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t166">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t166"><data value='create_chat_completion_stream_chunk'>create_chat_completion_stream_chunk</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t187">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t187"><data value='create_error_response'>create_error_response</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t197">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t197"><data value='messages_to_prompt'>messages_to_prompt</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t218">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t218"><data value='calculate_usage'>calculate_usage</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>96</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="0 96">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t32">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t32"><data value='init__'>MiniVLLMServer.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t57">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t57"><data value='setup_routes'>MiniVLLMServer._setup_routes</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t61">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t61"><data value='health_check'>MiniVLLMServer._setup_routes.health_check</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t70">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t70"><data value='list_models'>MiniVLLMServer._setup_routes.list_models</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t82">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t82"><data value='create_chat_completion'>MiniVLLMServer._setup_routes.create_chat_completion</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t131">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t131"><data value='create_completion'>MiniVLLMServer._setup_routes.create_completion</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t180">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t180"><data value='chat_completion_stream_generator'>MiniVLLMServer._chat_completion_stream_generator</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t223">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t223"><data value='completion_stream_generator'>MiniVLLMServer._completion_stream_generator</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t269">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t269"><data value='start_engine'>MiniVLLMServer.start_engine</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t273">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t273"><data value='shutdown'>MiniVLLMServer.shutdown</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t278">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t278"><data value='create_app'>create_app</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t283">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t283"><data value='startup_event'>create_app.startup_event</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t287">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t287"><data value='shutdown_event'>create_app.shutdown_event</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279___init___py.html">src/mini_vllm/cli/__init__.py</a></td>
                <td class="name left"><a href="z_83475db694906279___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html#t15">src/mini_vllm/cli/client.py</a></td>
                <td class="name left"><a href="z_83475db694906279_client_py.html#t15"><data value='chat_completion'>chat_completion</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html#t69">src/mini_vllm/cli/client.py</a></td>
                <td class="name left"><a href="z_83475db694906279_client_py.html#t69"><data value='health_check'>health_check</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html#t94">src/mini_vllm/cli/client.py</a></td>
                <td class="name left"><a href="z_83475db694906279_client_py.html#t94"><data value='parse_args'>parse_args</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html#t135">src/mini_vllm/cli/client.py</a></td>
                <td class="name left"><a href="z_83475db694906279_client_py.html#t135"><data value='interactive_chat'>interactive_chat</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html#t179">src/mini_vllm/cli/client.py</a></td>
                <td class="name left"><a href="z_83475db694906279_client_py.html#t179"><data value='main'>main</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html#t221">src/mini_vllm/cli/client.py</a></td>
                <td class="name left"><a href="z_83475db694906279_client_py.html#t221"><data value='main_sync'>main_sync</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html">src/mini_vllm/cli/client.py</a></td>
                <td class="name left"><a href="z_83475db694906279_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>2</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t17">src/mini_vllm/cli/serve.py</a></td>
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t17"><data value='setup_logging'>setup_logging</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t28">src/mini_vllm/cli/serve.py</a></td>
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t28"><data value='parse_args'>parse_args</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t66">src/mini_vllm/cli/serve.py</a></td>
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t66"><data value='create_engine_args'>create_engine_args</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t76">src/mini_vllm/cli/serve.py</a></td>
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t76"><data value='run_server'>run_server</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t119">src/mini_vllm/cli/serve.py</a></td>
                <td class="name left"><a href="z_83475db694906279_serve_py.html#t119"><data value='main'>main</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_serve_py.html">src/mini_vllm/cli/serve.py</a></td>
                <td class="name left"><a href="z_83475db694906279_serve_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>2</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333___init___py.html">src/mini_vllm/core/__init__.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t29">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t29"><data value='post_init__'>SamplingParams.__post_init__</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t45">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t45"><data value='create'>Request.create</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t65">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t65"><data value='is_finished'>Sequence.is_finished</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t70">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t70"><data value='get_len'>Sequence.get_len</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t74">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t74"><data value='add_token'>Sequence.add_token</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t78">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t78"><data value='get_output_text'>Sequence.get_output_text</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t94">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t94"><data value='from_request'>SequenceGroup.from_request</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t118">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t118"><data value='is_finished'>SequenceGroup.is_finished</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t122">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t122"><data value='get_main_sequence'>SequenceGroup.get_main_sequence</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t126">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t126"><data value='get_sequences'>SequenceGroup.get_sequences</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t141">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t141"><data value='batch_size'>ModelInput.batch_size</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t152">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t152"><data value='batch_size'>ModelOutput.batch_size</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t173">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t173"><data value='text'>RequestOutput.text</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t178">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t178"><data value='finish_reason'>RequestOutput.finish_reason</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t182">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t182"><data value='str__'>RequestOutput.__str__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t193">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t193"><data value='is_empty'>SchedulerOutput.is_empty</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>89</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="89 89">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t28">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t28"><data value='init__'>MiniEngine.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t52">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t52"><data value='add_request'>MiniEngine.add_request</data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t92">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t92"><data value='step'>MiniEngine.step</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t131">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t131"><data value='process_model_outputs'>MiniEngine._process_model_outputs</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t152">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t152"><data value='collect_finished_requests'>MiniEngine._collect_finished_requests</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t195">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t195"><data value='has_unfinished_requests'>MiniEngine.has_unfinished_requests</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t199">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t199"><data value='abort_request'>MiniEngine.abort_request</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t214">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t214"><data value='get_stats'>MiniEngine.get_stats</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t225">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t225"><data value='generate'>MiniEngine.generate</data></a></td>
                <td>20</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="17 20">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t24">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t24"><data value='init__'>MiniLLM.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t54">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t54"><data value='generate'>MiniLLM.generate</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t87">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t87"><data value='chat'>MiniLLM.chat</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t121">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t121"><data value='get_tokenizer'>MiniLLM.get_tokenizer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t125">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t125"><data value='get_stats'>MiniLLM.get_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t129">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t129"><data value='abort_request'>MiniLLM.abort_request</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t135">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t135"><data value='generate_text'>generate_text</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>15</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t34">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t34"><data value='init__'>MiniModelRunner.__init__</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t55">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t55"><data value='get_device'>MiniModelRunner._get_device</data></a></td>
                <td>5</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="3 5">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t64">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t64"><data value='prepare_inputs'>MiniModelRunner.prepare_inputs</data></a></td>
                <td>26</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="23 26">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t122">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t122"><data value='execute_model'>MiniModelRunner.execute_model</data></a></td>
                <td>14</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="13 14">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t158">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t158"><data value='sample_token'>MiniModelRunner._sample_token</data></a></td>
                <td>19</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="8 19">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t196">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t196"><data value='check_stop_condition'>MiniModelRunner.check_stop_condition</data></a></td>
                <td>12</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="7 12">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t220">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t220"><data value='get_tokenizer'>MiniModelRunner.get_tokenizer</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t27">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t27"><data value='init__'>MiniScheduler.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t38">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t38"><data value='add_sequence_group'>MiniScheduler.add_sequence_group</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t43">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t43"><data value='schedule'>MiniScheduler.schedule</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t77">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t77"><data value='schedule_decode'>MiniScheduler._schedule_decode</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t100">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t100"><data value='schedule_prefill'>MiniScheduler._schedule_prefill</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t118">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t118"><data value='update_finished'>MiniScheduler._update_finished</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t132">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t132"><data value='has_unfinished_requests'>MiniScheduler.has_unfinished_requests</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t136">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t136"><data value='get_finished_requests'>MiniScheduler.get_finished_requests</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t143">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t143"><data value='abort_request'>MiniScheduler.abort_request</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t161">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t161"><data value='get_stats'>MiniScheduler.get_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1024</td>
                <td>645</td>
                <td>19</td>
                <td class="right" data-ratio="379 1024">37%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 21:53 -0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
