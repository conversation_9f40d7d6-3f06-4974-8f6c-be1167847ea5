{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "28d8fb5de24de2a4bc1fef0c133641f7", "files": {"z_0b8761712507d4ef___init___py": {"hash": "99432a069eedaf1f4736fd8a2fa1d13c", "index": {"url": "z_0b8761712507d4ef___init___py.html", "file": "src/mini_vllm/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fd722886560870ab___init___py": {"hash": "978e5e6042cf48830b7c3cf4d7cd8ba8", "index": {"url": "z_fd722886560870ab___init___py.html", "file": "src/mini_vllm/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fd722886560870ab_async_engine_py": {"hash": "5c9ee9a4990af55174b1fdb46cb56861", "index": {"url": "z_fd722886560870ab_async_engine_py.html", "file": "src/mini_vllm/api/async_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 172, "n_excluded": 0, "n_missing": 172, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fd722886560870ab_protocol_py": {"hash": "9cf824c9dc59e6e84128fcd45db919db", "index": {"url": "z_fd722886560870ab_protocol_py.html", "file": "src/mini_vllm/api/protocol.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fd722886560870ab_server_py": {"hash": "8f32823ece9cfef15264a53e85378b35", "index": {"url": "z_fd722886560870ab_server_py.html", "file": "src/mini_vllm/api/server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83475db694906279___init___py": {"hash": "d2b29756d92e56b752a304490ddd24e2", "index": {"url": "z_83475db694906279___init___py.html", "file": "src/mini_vllm/cli/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83475db694906279_client_py": {"hash": "1081b04c8cbee3d36fa449d2c9131253", "index": {"url": "z_83475db694906279_client_py.html", "file": "src/mini_vllm/cli/client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 2, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_83475db694906279_serve_py": {"hash": "ed02f15c209977df8350dce4287748e0", "index": {"url": "z_83475db694906279_serve_py.html", "file": "src/mini_vllm/cli/serve.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 2, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fda62db419aca333___init___py": {"hash": "ae5bf1fa262ab765fb9d6e392d4224c3", "index": {"url": "z_fda62db419aca333___init___py.html", "file": "src/mini_vllm/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fda62db419aca333_data_structures_py": {"hash": "58cdf7c55fa027f20473af92ebaaeaf1", "index": {"url": "z_fda62db419aca333_data_structures_py.html", "file": "src/mini_vllm/core/data_structures.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fda62db419aca333_engine_py": {"hash": "5fce63879a7d72d845b4e6bd2b0d57f3", "index": {"url": "z_fda62db419aca333_engine_py.html", "file": "src/mini_vllm/core/engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 11, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fda62db419aca333_llm_py": {"hash": "2109cb4a598eb475c628425f4ed46ea7", "index": {"url": "z_fda62db419aca333_llm_py.html", "file": "src/mini_vllm/core/llm.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 15, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fda62db419aca333_model_runner_py": {"hash": "b40d5f4d47a2ed91feda23723730f4ba", "index": {"url": "z_fda62db419aca333_model_runner_py.html", "file": "src/mini_vllm/core/model_runner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fda62db419aca333_scheduler_py": {"hash": "b74eabafae37316ab9e672874db0049d", "index": {"url": "z_fda62db419aca333_scheduler_py.html", "file": "src/mini_vllm/core/scheduler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 81, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}