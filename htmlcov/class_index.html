<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">37%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 21:53 -0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0b8761712507d4ef___init___py.html">src/mini_vllm/__init__.py</a></td>
                <td class="name left"><a href="z_0b8761712507d4ef___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="9 13">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab___init___py.html">src/mini_vllm/api/__init__.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t18">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t18"><data value='AsyncRequestTracker'>AsyncRequestTracker</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t72">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html#t72"><data value='AsyncLLMEngine'>AsyncLLMEngine</data></a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html">src/mini_vllm/api/async_engine.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_async_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t11">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t11"><data value='ChatMessage'>ChatMessage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t16">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t16"><data value='ChatCompletionRequest'>ChatCompletionRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t29">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t29"><data value='CompletionRequest'>CompletionRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t41">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t41"><data value='UsageInfo'>UsageInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t47">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t47"><data value='ChatCompletionResponseChoice'>ChatCompletionResponseChoice</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t53">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t53"><data value='ChatCompletionResponse'>ChatCompletionResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t63">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t63"><data value='DeltaMessage'>DeltaMessage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t68">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t68"><data value='ChatCompletionStreamChoice'>ChatCompletionStreamChoice</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t74">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t74"><data value='ChatCompletionStreamResponse'>ChatCompletionStreamResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t83">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t83"><data value='CompletionResponseChoice'>CompletionResponseChoice</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t89">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t89"><data value='CompletionResponse'>CompletionResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t98">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t98"><data value='CompletionStreamChoice'>CompletionStreamChoice</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t104">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t104"><data value='CompletionStreamResponse'>CompletionStreamResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t113">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t113"><data value='ModelInfo'>ModelInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t120">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t120"><data value='ModelListResponse'>ModelListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t126">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t126"><data value='ErrorDetail'>ErrorDetail</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t132">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t132"><data value='ErrorResponse'>ErrorResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t137">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html#t137"><data value='HealthResponse'>HealthResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html">src/mini_vllm/api/protocol.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_protocol_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t29">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html#t29"><data value='MiniVLLMServer'>MiniVLLMServer</data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fd722886560870ab_server_py.html">src/mini_vllm/api/server.py</a></td>
                <td class="name left"><a href="z_fd722886560870ab_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279___init___py.html">src/mini_vllm/cli/__init__.py</a></td>
                <td class="name left"><a href="z_83475db694906279___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_client_py.html">src/mini_vllm/cli/client.py</a></td>
                <td class="name left"><a href="z_83475db694906279_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>117</td>
                <td>117</td>
                <td>2</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_83475db694906279_serve_py.html">src/mini_vllm/cli/serve.py</a></td>
                <td class="name left"><a href="z_83475db694906279_serve_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>53</td>
                <td>2</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333___init___py.html">src/mini_vllm/core/__init__.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t12">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t12"><data value='SequenceStatus'>SequenceStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t20">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t20"><data value='SamplingParams'>SamplingParams</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t37">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t37"><data value='Request'>Request</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t55">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t55"><data value='Sequence'>Sequence</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t86">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t86"><data value='SequenceGroup'>SequenceGroup</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t132">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t132"><data value='ModelInput'>ModelInput</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t146">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t146"><data value='ModelOutput'>ModelOutput</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t157">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t157"><data value='CompletionOutput'>CompletionOutput</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t165">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t165"><data value='RequestOutput'>RequestOutput</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t187">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html#t187"><data value='SchedulerOutput'>SchedulerOutput</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html">src/mini_vllm/core/data_structures.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_data_structures_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>89</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="89 89">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t18">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html#t18"><data value='MiniEngine'>MiniEngine</data></a></td>
                <td>78</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="67 78">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html">src/mini_vllm/core/engine.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t14">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html#t14"><data value='MiniLLM'>MiniLLM</data></a></td>
                <td>31</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="11 31">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html">src/mini_vllm/core/llm.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_llm_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>4</td>
                <td>15</td>
                <td class="right" data-ratio="13 17">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t22">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html#t22"><data value='MiniModelRunner'>MiniModelRunner</data></a></td>
                <td>86</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="64 86">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html">src/mini_vllm/core/model_runner.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_model_runner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t16">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html#t16"><data value='MiniScheduler'>MiniScheduler</data></a></td>
                <td>65</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="51 65">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html">src/mini_vllm/core/scheduler.py</a></td>
                <td class="name left"><a href="z_fda62db419aca333_scheduler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1024</td>
                <td>645</td>
                <td>19</td>
                <td class="right" data-ratio="379 1024">37%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 21:53 -0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
